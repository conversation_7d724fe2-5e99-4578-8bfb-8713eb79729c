/* Trace component specific styles */

/* Red-themed background for trace section */
.trace-section {
  background-color: rgba(254, 226, 226, 0.7); /* Light red background with transparency */
  border: 1px solid rgba(239, 68, 68, 0.3); /* Red border with transparency */
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

/* Trace section header */
.trace-section-header {
  color: rgb(185, 28, 28); /* Dark red text */
  font-weight: 600;
}

/* Trace message table container - fixed height for exactly 15 rows */
.trace-message-table-container {
  background-color: white;
  border-radius: 0.375rem;
  overflow: hidden;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  /* Fixed height calculation: header (2.5rem) + 15 rows (1.75rem each) + padding */
  height: calc(2.5rem + 15 * 1.75rem + 0.5rem);
  max-height: calc(2.5rem + 15 * 1.75rem + 0.5rem);
  display: flex;
  flex-direction: column;
}

/* Table wrapper with scrolling */
.trace-message-table-wrapper {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  /* Disable CSS smooth scrolling - GSAP will handle it */
  scroll-behavior: auto;
  /* Enable hardware acceleration for better performance */
  transform: translateZ(0);
  will-change: scroll-position;
}

/* Table styling */
.trace-message-table-wrapper table {
  min-width: 100%;
  table-layout: fixed;
}

/* Ensure table headers stay visible during scroll */
.trace-message-table-wrapper thead th {
  position: sticky;
  top: 0;
  background-color: white;
  z-index: 10;
  box-shadow: 0 1px 0 0 rgba(0, 0, 0, 0.1);
  height: 2.5rem;
}

/* Table row height consistency */
.trace-message-table-wrapper tbody tr {
  height: 1.75rem;
  min-height: 1.75rem;
  max-height: 1.75rem;
  /* Enable hardware acceleration for smooth animations */
  transform: translateZ(0);
  will-change: transform, opacity, background-color;
  /* Default visible state for existing rows */
  opacity: 1;
  transform: translateY(0);
  transition: background-color 0.3s ease;
}

/* Initial state for new rows that will be animated in */
.trace-message-table-wrapper tbody tr.trace-row-new {
  opacity: 0;
  transform: translateY(-20px);
}

/* Selected message row */
.trace-message-selected {
  background-color: rgba(239, 68, 68, 0.1) !important;
  border-left: 3px solid rgb(239, 68, 68) !important;
}

/* Message type badges */
.badge-publish {
  background-color: rgba(59, 130, 246, 0.1);
  color: rgb(59, 130, 246);
  border-color: rgb(59, 130, 246);
}

.badge-subscribe {
  background-color: rgba(139, 92, 246, 0.1);
  color: rgb(139, 92, 246);
  border-color: rgb(139, 92, 246);
}

.badge-connect {
  background-color: rgba(16, 185, 129, 0.1);
  color: rgb(16, 185, 129);
  border-color: rgb(16, 185, 129);
}

/* Message details panel */
.trace-details-panel {
  background-color: white;
  border-radius: 0.375rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

/* Payload display area */
.trace-payload-display {
  font-family: monospace;
  background-color: rgba(243, 244, 246, 0.7);
  border-radius: 0.25rem;
  padding: 0.5rem;
  min-height: 100px;
  max-height: 200px;
  overflow-y: auto;
}

/* Message direction visual indicators - subtle and stable */
.trace-message-row {
  transition: background-color 0.2s ease-out, box-shadow 0.2s ease-out;
}

/* Direction indicators with meaningful colors */
.trace-message-in {
  border-left: 3px solid rgba(34, 197, 94, 0.4);
}

.trace-message-out {
  border-left: 3px solid rgba(234, 179, 8, 0.4);
}

.trace-message-neutral {
  border-left: 3px solid rgba(156, 163, 175, 0.4);
}

/* Only animate on hover for existing rows - very subtle */
.trace-message-row:hover {
  background-color: rgba(59, 130, 246, 0.03) !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

/* New packet highlight animation - matching infinite_scroll_packets.html */
.trace-message-row.new-packet {
  background: linear-gradient(90deg, #4caf50, transparent);
  animation: highlight 2s ease-out;
}

@keyframes highlight {
  0% {
    background: linear-gradient(90deg, #4caf50, transparent);
  }
  100% {
    background: transparent;
  }
}

/* Legacy support for newly-added class */
.trace-message-row.newly-added {
  animation: new-row-highlight 0.4s ease-out;
}

@keyframes new-row-highlight {
  0% {
    background-color: rgba(59, 130, 246, 0.12);
    box-shadow: 0 2px 6px rgba(59, 130, 246, 0.2);
  }
  100% {
    background-color: transparent;
    box-shadow: none;
  }
}

/* JavaScript-controlled row highlighting - more prominent */
.trace-message-row.js-highlighted {
  background-color: rgba(59, 130, 246, 0.15) !important;
  border-left: 5px solid rgb(59, 130, 246) !important;
  box-shadow: 0 4px 16px rgba(59, 130, 246, 0.25);
  transform: translateX(3px);
  transition: all 0.2s ease-out;
}

/* Ensure JS highlight takes precedence over server-side selection */
.trace-message-row.js-highlighted.trace-message-selected {
  background-color: rgba(59, 130, 246, 0.18) !important;
  border-left-color: rgb(59, 130, 246) !important;
}

/* Enhanced pulse animation for clicked rows */
@keyframes row-highlight-pulse {
  0% {
    background-color: rgba(59, 130, 246, 0.25);
    box-shadow: 0 6px 20px rgba(59, 130, 246, 0.35);
    transform: translateX(4px);
  }
  50% {
    background-color: rgba(59, 130, 246, 0.2);
    box-shadow: 0 5px 18px rgba(59, 130, 246, 0.3);
    transform: translateX(3.5px);
  }
  100% {
    background-color: rgba(59, 130, 246, 0.15);
    box-shadow: 0 4px 16px rgba(59, 130, 246, 0.25);
    transform: translateX(3px);
  }
}

/* GSAP animation classes for new rows */
.trace-message-row.gsap-animating {
  /* Ensure GSAP animations take precedence */
  animation: none !important;
}

/* Ensure new packet animations work with GSAP */
.trace-message-row.new-packet.gsap-animating {
  /* Allow both GSAP transforms and CSS background animations */
  animation: highlight 2s ease-out !important;
}

/* Smooth scrolling indicator */
.trace-message-table-wrapper.smooth-scrolling {
  /* Visual indicator during smooth scroll */
  box-shadow: inset 0 0 10px rgba(59, 130, 246, 0.1);
}

/* Remove the duplicate badge animations - they're handled below */

/* Table cell truncation styles - adjusted for better ClientID visibility */
.table-cell-truncate {
  max-width: 180px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.payload-preview-truncate {
  max-width: 250px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* ClientID specific styling */
.trace-client-id {
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Timestamp specific styling - fixed width for exactly 18 characters */
.trace-timestamp {
  font-family: monospace;
  width: 144px; /* 18 characters * 8px per character in monospace */
  min-width: 144px;
  max-width: 144px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: clip; /* Don't use ellipsis since we control the length */
}

/* Loading row animation */
.trace-loading-row {
  animation: loading-pulse 1.5s ease-in-out infinite;
}

@keyframes loading-pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* Responsive adjustments for smaller screens */
@media (max-width: 768px) {
  .trace-message-table-container {
    /* Adjust height for mobile - fewer visible rows */
    height: calc(2.5rem + 10 * 1.75rem + 0.5rem);
    max-height: calc(2.5rem + 10 * 1.75rem + 0.5rem);
  }

  .table-cell-truncate {
    max-width: 120px;
  }

  .payload-preview-truncate {
    max-width: 150px;
  }

  .trace-client-id {
    max-width: 140px;
  }
}

@media (max-width: 480px) {
  .trace-message-table-container {
    /* Further reduce for very small screens */
    height: calc(2.5rem + 8 * 1.75rem + 0.5rem);
    max-height: calc(2.5rem + 8 * 1.75rem + 0.5rem);
  }

  .table-cell-truncate {
    max-width: 100px;
  }

  .payload-preview-truncate {
    max-width: 120px;
  }

  .trace-client-id {
    max-width: 110px;
  }
}

/* Smooth scrollbar styling */
.trace-message-table-wrapper::-webkit-scrollbar {
  width: 6px;
}

.trace-message-table-wrapper::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 3px;
}

.trace-message-table-wrapper::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.3);
  border-radius: 3px;
}

.trace-message-table-wrapper::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.5);
}

/* Ensure smooth transitions for interactive elements - very subtle */
.trace-direction,
.trace-message-type,
.trace-client-id {
  transition: transform 0.15s ease-out;
}

/* Subtle hover effects for badges only */
.trace-direction:hover,
.trace-message-type:hover {
  transform: scale(1.02);
}

/* JSON Viewer Geeky Styling */
.geeky-json-viewer {
  --json-viewer-background: #0f1419;
  --json-viewer-color: #e6e6e6;
  --json-viewer-string-color: #86efac;
  --json-viewer-number-color: #60a5fa;
  --json-viewer-boolean-color: #f59e0b;
  --json-viewer-null-color: #ef4444;
  --json-viewer-undefined-color: #8b5cf6;
  --json-viewer-function-color: #06b6d4;
  --json-viewer-rotate-color: #9ca3af;
  --json-viewer-key-color: #fbbf24;
  --json-viewer-url-color: #34d399;
  --json-viewer-date-color: #fb7185;

  background: var(--json-viewer-background);
  color: var(--json-viewer-color);
  font-family: 'JetBrains Mono', 'Fira Code', 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.4;
  border-radius: 8px;
  border: 1px solid #374151;
  padding: 16px;
  max-height: 400px;
  overflow-y: auto;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);

  /* Custom scrollbar for geeky theme */
  scrollbar-width: thin;
  scrollbar-color: #4b5563 #1f2937;
}

.geeky-json-viewer::-webkit-scrollbar {
  width: 8px;
}

.geeky-json-viewer::-webkit-scrollbar-track {
  background: #1f2937;
  border-radius: 4px;
}

.geeky-json-viewer::-webkit-scrollbar-thumb {
  background: #4b5563;
  border-radius: 4px;
}

.geeky-json-viewer::-webkit-scrollbar-thumb:hover {
  background: #6b7280;
}

/* JSON Viewer Error Styling */
.json-viewer-error {
  background: #1f2937;
  border: 1px solid #ef4444;
  border-radius: 8px;
  padding: 16px;
  font-family: 'JetBrains Mono', 'Fira Code', 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  max-height: 400px;
  overflow-y: auto;
}

.json-error-header {
  color: #ef4444;
  font-weight: 600;
  font-size: 14px;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #374151;
}

.json-raw-content {
  color: #e5e7eb;
  font-size: 12px;
  line-height: 1.4;
  white-space: pre-wrap;
  word-break: break-word;
  margin: 0;
}

/* Ensure JSON viewer integrates well with existing card design */
.json-viewer-container {
  background: transparent;
  border-radius: 0;
  padding: 0;
}

/* Override default json-viewer styles for better integration */
.geeky-json-viewer json-viewer {
  background: transparent !important;
  font-family: inherit !important;
}

/* Style the expand/collapse buttons */
.geeky-json-viewer .json-viewer__expand-icon {
  color: var(--json-viewer-rotate-color) !important;
  transition: transform 0.2s ease;
}

.geeky-json-viewer .json-viewer__expand-icon:hover {
  color: var(--json-viewer-key-color) !important;
  transform: scale(1.1);
}

/* Style property keys */
.geeky-json-viewer .json-viewer__key {
  color: var(--json-viewer-key-color) !important;
  font-weight: 500;
}

/* Style different value types */
.geeky-json-viewer .json-viewer__value--string {
  color: var(--json-viewer-string-color) !important;
}

.geeky-json-viewer .json-viewer__value--number {
  color: var(--json-viewer-number-color) !important;
}

.geeky-json-viewer .json-viewer__value--boolean {
  color: var(--json-viewer-boolean-color) !important;
  font-weight: 600;
}

.geeky-json-viewer .json-viewer__value--null {
  color: var(--json-viewer-null-color) !important;
  font-weight: 600;
}

.geeky-json-viewer .json-viewer__value--undefined {
  color: var(--json-viewer-undefined-color) !important;
  font-style: italic;
}

/* Style brackets and punctuation */
.geeky-json-viewer .json-viewer__bracket,
.geeky-json-viewer .json-viewer__comma,
.geeky-json-viewer .json-viewer__colon {
  color: #9ca3af !important;
}

/* Hover effects for interactive elements */
.geeky-json-viewer .json-viewer__key:hover {
  color: #fde047 !important;
  cursor: pointer;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .geeky-json-viewer {
    font-size: 12px;
    padding: 12px;
    max-height: 300px;
  }

  .json-viewer-error {
    padding: 12px;
    max-height: 300px;
  }

  .json-raw-content {
    font-size: 11px;
  }
}
